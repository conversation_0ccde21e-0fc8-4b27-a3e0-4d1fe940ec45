.login{
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: grid;
}
.login-container{
    place-self: center;
    width: max(23vw,330px);
    color: gray;
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 25px;
    padding: 25px 30px;
    font-size: 14px;
    animation: fadeIn 0.5s;
}
.login-title{
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: black;
}
.login-title img{
    width: 18px;
    cursor: pointer;
}    
.login-inputs{
        display: flex;
        flex-direction: column;
        gap: 15px;
}
.login-inputs input{
    outline: none;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}
.login-container button{
    background-color: rgb(14, 14, 105);
    color: white;
    border: none;
    padding: 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 15px;
}
.login-condition{
    display: flex;
    align-items: start;
    gap: 7px;
    margin-top: -15px;
}
.login-condition input{
    margin-top: 5px;
}
.login p span{
    color: rgb(31, 39, 112);
    cursor: pointer;
    font-weight: 500;
}