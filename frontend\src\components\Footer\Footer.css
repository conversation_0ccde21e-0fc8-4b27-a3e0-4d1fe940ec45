.footer{
    color: white;
    background-color: #1e1e1e;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 20px 8vw;
    padding-top: 80px;
    margin-top: 100px;
}
.footer-content{
    width: 100%;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 80px;
}
.footer-content-left , .footer-content-center , .footer-content-right{
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 20px;
}
.footer-content-left li , .footer-content-center li , .footer-content-right li{
    list-style: none;
    margin-bottom: 10px;
    cursor: pointer;
}
.footer-content-right h2, .footer-content-center h2{
    color: white;
}
.footer-social-icons img{
    width: 40px;
    margin-right: 15px;
    
}
.footer hr{
    width: 100%;
    height: 2px;
    margin: 20px 0;
    background-color: gray;
    border: none;
}
@media (max-width: 750px) {
    .footer-content{
        display: flex;
        flex-direction: column;
        gap: 35px;
    }
    .footer-copyright{
        text-align: center;
    }
}

