import foodModel from "../models/foodModel.js";
import fs from 'fs';


//add food item
const addFood = async (req, res) => {
    let image_filename = `${req.file.filename}`;

    const food = new foodModel({
        name: req.body.name,
        image: image_filename,
        price: req.body.price,
        description: req.body.description,
        category: req.body.category
    });
    try{
        await food.save();
        res.json({success: true, message: "Food item added successfully"});
    } catch (error){
        console.log(error)
        res.json({success: false, message: "Error"});
    }

}

// all food list
const listFood=async(req,res)=>{
    try{
        const food = await foodModel.find({});
        res.json({success: true,data:food});
    } catch (error){
        console.log(error) 
        res.json({success: false, message: "Error"});
    }
}

// remove food item
const removeFood = async (req, res) => {
    try {
        await foodModel.findByIdAndDelete(req.params.id);
        res.json({ success: true, message: "Food item removed successfully" });
    } catch (error) {
        console.log(error);
        res.json({ success: false, message: "Error" });
    }
}

export { addFood,listFood,removeFood };
