# Dependencies
node_modules/
frontend/node_modules/
backend/node_modules/
admin/node_modules/

# Build outputs
frontend/dist/
frontend/build/
backend/dist/
backend/build/
admin/dist/
admin/build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
backend/.env
frontend/.env
admin/.env

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS files
Thumbs.db
.DS_Store

# Testing
coverage/
.nyc_output/

# Temporary files
*.tmp
*.temp
.cache/

# Package lock files (optional - uncomment if you want to ignore them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

